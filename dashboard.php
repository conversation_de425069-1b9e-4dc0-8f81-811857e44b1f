<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Tableau de Bord - Ministère</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            font-family: 'Segoe UI', sans-serif;
            background-color: #f5f7fa;
        }

        /* Barre du haut */
        header {
            background-color: #34495e;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 30px;
        }

        .titre-header {
            font-size: 20px;
            font-weight: bold;
        }

        .btn-deconnexion {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
        }

        .container {
            padding: 30px;
        }

        .section-cartes {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 40px;
        }

        .carte {
            flex: 1;
            min-width: 250px;
            background-color: #ffffff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.2s, background-color 0.2s;
            cursor: pointer;
            text-decoration: none;
            color: #2c3e50;
        }

        .carte:hover {
            transform: scale(1.02);
            background-color: #ecf0f1;
        }

        .carte h3 {
            margin-bottom: 10px;
        }

        .section-actions {
            background-color: #ffffff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .section-actions h2 {
            margin-bottom: 20px;
            color: #2c3e50;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
        }

        .action {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            transition: background-color 0.2s;
            cursor: pointer;
        }

        .action:hover {
            background-color: #d0d7de;
        }

        footer {
            margin-top: 50px;
            background-color: #34495e;
            color: white;
            text-align: center;
            padding: 15px;
        }

        @media (max-width: 600px) {
            header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .carte {
                width: 100%;
            }
        }
    </style>
</head>
<body>

<header>
    <div class="titre-header">Tableau de Bord</div>
    <button class="btn-deconnexion">Déconnexion</button>
</header>

<div class="container">

    <!-- Cartes principales -->
    <div class="section-cartes">
        <a href="gestion utilisateur.php" class="carte">
            <h3>Gestion des Utilisateurs</h3>
            <p>Accéder à la liste, ajout ou modification des utilisateurs.</p>
        </a>
        <a href="gestion centres.php" class="carte">
            <h3>Gestion des Centres</h3>
            <p>Voir, ajouter ou modifier les centres d'accueil.</p>
        </a>
        <a href="gestion partenaires.php" class="carte">
            <h3>Gestion des Partenaires</h3>
            <p>Gérer les partenaires institutionnels et ONG.</p>
        </a>
    </div>

    <!-- Actions rapides -->
    <div class="section-actions">
        <h2>Actions Rapides</h2>
        <div class="actions-grid">
            <div class="action" onclick="window.location='orphelinats_gestion.php'">Liste des Orphelinats</div>
            <div class="action" onclick="window.location='gestion_enfant.php '">Gestion des Enfants</div>
            <div class="action" onclick="window.location='signalements.php'">Signalement</div>
            <div class="action" onclick="window.location='suivi_enfants.php'">Suivi des Enfants</div>
            <div class="action" onclick="window.location='parrainage.php'">Parrainage</div>
        </div>
    </div>
</div>

<footer>
    &copy; 2025 Ministère de la Solidarité Nationale - Tous droits réservés.
</footer>

</body>
</html>
