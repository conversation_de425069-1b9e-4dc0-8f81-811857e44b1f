<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administration - Gestion des Structures d'Accueil</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .map-container {
            height: 350px;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .admin-card {
            transition: all 0.3s ease;
        }
        
        .admin-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .coordinate-display {
            background: #f3f4f6;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .coordinate-display.selected {
            background: #dcfce7;
            border-color: #22c55e;
            color: #15803d;
        }
        
        @media (prefers-color-scheme: dark) {
            .coordinate-display {
                background: #374151;
                border-color: #6b7280;
                color: #d1d5db;
            }
            
            .coordinate-display.selected {
                background: #14532d;
                border-color: #22c55e;
                color: #22c55e;
            }
        }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 font-sans">
    
    <!-- Admin Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-3">
                    <span class="text-2xl">⚙️</span>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900 dark:text-white">
                            Administration - Structures d'Accueil
                        </h1>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Gestion des centres d'accueil pour enfants en détresse
                        </p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        <span class="font-medium">Admin:</span> Jean Dupont
                    </div>
                    <button class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm">
                        🚪 Déconnexion
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        
        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="admin-card bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                        <span class="text-xl">🏠</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Structures Actives</p>
                        <p id="totalStructures" class="text-2xl font-bold text-gray-900 dark:text-white">12</p>
                    </div>
                </div>
            </div>
            
            <div class="admin-card bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
                        <span class="text-xl">✅</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Validées</p>
                        <p id="validatedStructures" class="text-2xl font-bold text-gray-900 dark:text-white">8</p>
                    </div>
                </div>
            </div>
            
            <div class="admin-card bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                        <span class="text-xl">⏳</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">En Attente</p>
                        <p id="pendingStructures" class="text-2xl font-bold text-gray-900 dark:text-white">4</p>
                    </div>
                </div>
            </div>
            
            <div class="admin-card bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
                        <span class="text-xl">👥</span>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Capacité Totale</p>
                        <p id="totalCapacity" class="text-2xl font-bold text-gray-900 dark:text-white">847</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add New Structure Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                    ➕ Enregistrer une Nouvelle Structure
                </h2>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    Les champs marqués * sont obligatoires
                </div>
            </div>
            
            <form id="structureForm" class="space-y-6">
                
                <!-- Basic Information -->
                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        📋 Informations Générales
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Nom de la Structure *
                            </label>
                            <input type="text" id="nom" name="nom" required
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-base 
                                          bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                          focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Ex: Orphelinat Espoir">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Type de Structure *
                            </label>
                            <select id="type_structure_id" name="type_structure_id" required
                                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-base
                                           bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                           focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Sélectionner un type</option>
                                <option value="1">Orphelinat</option>
                                <option value="2">ONG</option>
                                <option value="4">Centre d'Encadrement</option>
                                <option value="5">Famille d'Accueil</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Adresse Complète *
                        </label>
                        <textarea id="adresse" name="adresse" required rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-base
                                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                         focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Adresse complète avec quartier, commune, province..."></textarea>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        📞 Informations de Contact
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Téléphone *
                            </label>
                            <input type="tel" id="telephone" name="telephone" required
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-base
                                          bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                          focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Ex: 79123456">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Email
                            </label>
                            <input type="email" id="email" name="email"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-base
                                          bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                          focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="<EMAIL>">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Responsable *
                            </label>
                            <input type="text" id="responsable" name="responsable" required
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-base
                                          bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                          focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Nom du responsable">
                        </div>
                    </div>
                </div>

                <!-- Capacity Information -->
                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        🏠 Capacité d'Accueil
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Capacité Maximale *
                            </label>
                            <input type="number" id="capacite_max" name="capacite_max" required min="1"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-base
                                          bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                          focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Ex: 50">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Capacité Actuelle
                            </label>
                            <input type="number" id="capacite_actuelle" name="capacite_actuelle" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-base
                                          bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                                          focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   placeholder="Ex: 35">
                        </div>
                    </div>
                </div>

                <!-- Geolocation -->
                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        📍 Localisation GPS
                    </h3>
                    
                    <div class="space-y-4">
                        <!-- Coordinate Display -->
                        <div id="coordinateDisplay" class="coordinate-display">
                            <div class="text-lg font-semibold mb-2">
                                📍 Cliquez sur la carte pour sélectionner la position
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                Les coordonnées GPS permettront aux utilisateurs de trouver facilement votre structure
                            </div>
                        </div>
                        
                        <!-- Coordinate Inputs -->
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Latitude *
                                </label>
                                <input type="number" id="latitude" name="latitude" step="any" required readonly
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-base
                                              bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white"
                                       placeholder="Ex: -3.3614">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Longitude *
                                </label>
                                <input type="number" id="longitude" name="longitude" step="any" required readonly
                                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-base
                                              bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white"
                                       placeholder="Ex: 29.3599">
                            </div>
                        </div>
                        
                        <!-- Interactive Map -->
                        <div>
                            <div id="locationMap" class="map-container border border-gray-300 dark:border-gray-600"></div>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-2">
                                💡 <strong>Instructions:</strong> Cliquez sur la carte pour placer un marqueur à l'emplacement exact de votre structure.
                                Vous pouvez zoomer et déplacer la carte pour une sélection précise.
                            </p>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="flex space-x-3">
                            <button type="button" onclick="getCurrentLocationAdmin()" 
                                    class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm flex items-center space-x-2">
                                <span>📍</span><span>Ma Position Actuelle</span>
                            </button>
                            <button type="button" onclick="searchLocation()" 
                                    class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded text-sm flex items-center space-x-2">
                                <span>🔍</span><span>Rechercher Adresse</span>
                            </button>
                            <button type="button" onclick="clearLocation()" 
                                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded text-sm flex items-center space-x-2">
                                <span>🗑️</span><span>Effacer</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-600">
                    <button type="button" onclick="resetForm()"
                            class="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 
                                   hover:bg-gray-200 dark:hover:bg-gray-500 rounded-lg transition">
                        🔄 Réinitialiser
                    </button>
                    <button type="submit"
                            class="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition flex items-center space-x-2">
                        <span>✅</span><span>Enregistrer Structure</span>
                    </button>
                </div>
            </form>
        </div>

        <!-- Recent Structures -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">
                📋 Structures Récemment Ajoutées
            </h2>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Structure
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Type
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Capacité
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Position GPS
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody id="recentStructures" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <!-- Will be populated dynamically -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // Dark mode support
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.documentElement.classList.add('dark');
        }
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
            if (event.matches) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        });

        // Global variables
        let locationMap;
        let selectedMarker = null;
        let recentStructures = [];

        // Sample recent structures
        const sampleRecentStructures = [
            {
                id: 1,
                nom: "Orphelinat Igikundiro",
                type: "Orphelinat",
                capacite_max: 42,
                capacite_actuelle: 39,
                latitude: -3.3614,
                longitude: 29.3599,
                statut: "Validé",
                date_creation: "2025-01-20"
            },
            {
                id: 2,
                nom: "Centre Ejo Heza",
                type: "Centre d'Encadrement",
                capacite_max: 33,
                capacite_actuelle: 32,
                latitude: -3.4139,
                longitude: 29.3794,
                statut: "En attente",
                date_creation: "2025-01-19"
            }
        ];

        // Initialize the application
        function initApp() {
            initLocationMap();
            loadRecentStructures();
            updateStatistics();
        }

        // Initialize the location selection map
        function initLocationMap() {
            // Center on Bujumbura, Burundi
            locationMap = L.map('locationMap').setView([-3.3614, 29.3599], 12);

            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(locationMap);

            // Handle map clicks
            locationMap.on('click', function(e) {
                const lat = e.latlng.lat;
                const lng = e.latlng.lng;
                setLocation(lat, lng, 'Sélection manuelle');
            });
        }

        // Set location coordinates with source information
        function setLocation(lat, lng, source = '') {
            // Remove existing marker
            if (selectedMarker) {
                locationMap.removeLayer(selectedMarker);
            }

            // Add new marker
            selectedMarker = L.marker([lat, lng], {
                icon: L.divIcon({
                    className: 'flex items-center justify-center w-8 h-8 rounded-full',
                    html: '<div style="background: #22c55e; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; border: 3px solid white; box-shadow: 0 2px 8px rgba(0,0,0,0.3);">📍</div>',
                    iconSize: [32, 32],
                    iconAnchor: [16, 16]
                })
            }).addTo(locationMap);

            // Update coordinate inputs with high precision
            document.getElementById('latitude').value = lat.toFixed(8);
            document.getElementById('longitude').value = lng.toFixed(8);

            // Update coordinate display
            updateCoordinateDisplay(lat, lng, source);

            selectedMarker.bindPopup(`
                <div class="text-center p-2">
                    <strong>📍 Position Sélectionnée</strong><br>
                    <small>Lat: ${lat.toFixed(8)}<br>Lng: ${lng.toFixed(8)}</small><br>
                    <small class="text-gray-600">${source}</small>
                </div>
            `).openPopup();
        }

        // Update coordinate display with source info
        function updateCoordinateDisplay(lat, lng, source = '') {
            const display = document.getElementById('coordinateDisplay');
            display.classList.add('selected');
            display.innerHTML = `
                <div class="text-lg font-semibold mb-2">
                    ✅ Position GPS Sélectionnée
                </div>
                <div class="text-sm">
                    <strong>Latitude:</strong> ${lat.toFixed(8)} | <strong>Longitude:</strong> ${lng.toFixed(8)}<br>
                    ${source ? `<span class="text-gray-600 dark:text-gray-400">Source: ${source}</span>` : ''}
                </div>
            `;
        }

        // Get current location with higher accuracy for admin
        function getCurrentLocationAdmin() {
            if (navigator.geolocation) {
                // Show loading state
                const btn = event.target;
                const originalText = btn.innerHTML;
                btn.innerHTML = '<span>⏳</span><span>Localisation...</span>';
                btn.disabled = true;

                const options = {
                    enableHighAccuracy: true,
                    timeout: 20000,
                    maximumAge: 30000 // 30 seconds cache
                };

                navigator.geolocation.getCurrentPosition(
                    position => {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        const accuracy = position.coords.accuracy;
                        
                        console.log(`Position admin trouvée: Lat ${lat}, Lng ${lng}, Précision: ${accuracy}m`);
                        
                        setLocation(lat, lng, `GPS (±${accuracy.toFixed(0)}m)`);
                        locationMap.setView([lat, lng], 16);
                        
                        showCustomAlert('Position trouvée', `Votre position a été localisée avec une précision de ${accuracy.toFixed(0)} mètres. Vous pouvez l'ajuster en cliquant sur la carte.`, 'success');
                        
                        // Reset button
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    },
                    error => {
                        console.error('Geolocation error:', error);
                        let errorMessage = 'Impossible d\'obtenir votre position. ';
                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage += 'Permission refusée. Autorisez la géolocalisation dans les paramètres du navigateur.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage += 'Position non disponible.';
                                break;
                            case error.TIMEOUT:
                                errorMessage += 'Délai de localisation expiré.';
                                break;
                            default:
                                errorMessage += 'Erreur inconnue.';
                                break;
                        }
                        showCustomAlert('Erreur de géolocalisation', errorMessage, 'error');
                        
                        // Reset button
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    },
                    options
                );
            } else {
                showCustomAlert('Géolocalisation non supportée', 'Votre navigateur ne supporte pas la géolocalisation.', 'error');
            }
        }

        // Search for a location (simplified)
        function searchLocation() {
            const address = document.getElementById('adresse').value;
            if (!address) {
                showCustomAlert('Adresse requise', 'Veuillez d\'abord remplir le champ adresse pour la recherche.', 'error');
                return;
            }

            // For demo purposes - in production, use Nominatim or similar geocoding service
            showCustomAlert('Recherche en cours', `Recherche de "${address}" en cours... (Fonctionnalité à implémenter avec un service de géocodage)`, 'info');
        }

        // Clear location selection
        function clearLocation() {
            if (selectedMarker) {
                locationMap.removeLayer(selectedMarker);
                selectedMarker = null;
            }

            document.getElementById('latitude').value = '';
            document.getElementById('longitude').value = '';

            const display = document.getElementById('coordinateDisplay');
            display.classList.remove('selected');
            display.innerHTML = `
                <div class="text-lg font-semibold mb-2">
                    📍 Cliquez sur la carte pour sélectionner la position
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    Les coordonnées GPS permettront aux utilisateurs de trouver facilement votre structure
                </div>
            `;
        }

        // Handle form submission
        document.getElementById('structureForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Collect form data
            const formData = new FormData(this);
            const structureData = Object.fromEntries(formData.entries());
            
            // Validate coordinates
            if (!structureData.latitude || !structureData.longitude) {
                showCustomAlert('Position GPS requise', 'Veuillez sélectionner la position GPS en cliquant sur la carte.', 'error');
                return;
            }

            // Additional validation for coordinate accuracy
            const lat = parseFloat(structureData.latitude);
            const lng = parseFloat(structureData.longitude);
            
            if (Math.abs(lat) > 90 || Math.abs(lng) > 180) {
                showCustomAlert('Coordonnées invalides', 'Les coordonnées GPS semblent incorrectes. Veuillez les vérifier.', 'error');
                return;
            }

            // Simulate API call to save structure
            submitStructure(structureData);
        });

        // Submit structure to database (simulation)
        function submitStructure(data) {
            // Show loading state
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span>⏳</span><span>Enregistrement...</span>';
            submitBtn.disabled = true;

            // Simulate API call
            setTimeout(() => {
                // Add to recent structures
                const newStructure = {
                    id: Date.now(),
                    nom: data.nom,
                    type: getTypeNameById(data.type_structure_id),
                    capacite_max: parseInt(data.capacite_max),
                    capacite_actuelle: parseInt(data.capacite_actuelle) || 0,
                    latitude: parseFloat(data.latitude),
                    longitude: parseFloat(data.longitude),
                    statut: "En attente",
                    date_creation: new Date().toISOString().split('T')[0]
                };

                recentStructures.unshift(newStructure);
                loadRecentStructures();
                updateStatistics();

                // Reset form
                resetForm();

                // Show success message with coordinates
                showCustomAlert('Structure enregistrée', 
                    `La structure "${data.nom}" a été enregistrée avec succès aux coordonnées ${parseFloat(data.latitude).toFixed(6)}, ${parseFloat(data.longitude).toFixed(6)} et est en attente de validation.`, 
                    'success');

                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;

                // Here you would normally send data to your PHP backend:
                /*
                fetch('api/add_structure.php', {
                    method: 'POST',
                    body: new FormData(document.getElementById('structureForm'))
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        showCustomAlert('Structure enregistrée', 'Structure ajoutée avec succès', 'success');
                        resetForm();
                    } else {
                        showCustomAlert('Erreur', result.message || 'Erreur lors de l\'enregistrement', 'error');
                    }
                });
                */

            }, 2000); // Simulate 2 second delay
        }

        // Get type name by ID
        function getTypeNameById(id) {
            const types = {
                '1': 'Orphelinat',
                '2': 'ONG',
                '4': 'Centre d\'Encadrement',
                '5': 'Famille d\'Accueil'
            };
            return types[id] || 'Inconnu';
        }

        // Reset form
        function resetForm() {
            document.getElementById('structureForm').reset();
            clearLocation();
        }

        // Load recent structures
        function loadRecentStructures() {
            const tbody = document.getElementById('recentStructures');
            
            if (recentStructures.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                            Aucune structure récente
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = recentStructures.slice(0, 5).map(structure => {
                const statusColor = structure.statut === 'Validé' ? 'green' : 
                                  structure.statut === 'En attente' ? 'yellow' : 'red';
                
                return `
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div>
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        ${structure.nom}
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        Ajouté le ${structure.date_creation}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="text-sm text-gray-900 dark:text-white">${structure.type}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="text-sm text-gray-900 dark:text-white">
                                ${structure.capacite_actuelle}/${structure.capacite_max}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-xs text-gray-600 dark:text-gray-400">
                                ${structure.latitude.toFixed(6)}<br>
                                ${structure.longitude.toFixed(6)}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <button onclick="editStructure(${structure.id})" 
                                        class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                                    ✏️ Modifier
                                </button>
                                <button onclick="viewOnMap(${structure.latitude}, ${structure.longitude})" 
                                        class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                                    🗺️ Carte
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // View structure on map
        function viewOnMap(lat, lng) {
            locationMap.setView([lat, lng], 16);
            if (selectedMarker) {
                locationMap.removeLayer(selectedMarker);
            }
            setLocation(lat, lng, 'Structure existante');
        }

        // Update statistics
        function updateStatistics() {
            document.getElementById('totalStructures').textContent = recentStructures.length + 10; // +10 for existing structures
            
            const validated = recentStructures.filter(s => s.statut === 'Validé').length + 8;
            document.getElementById('validatedStructures').textContent = validated;
            
            const pending = recentStructures.filter(s => s.statut === 'En attente').length;
            document.getElementById('pendingStructures').textContent = pending;
            
            const totalCapacity = recentStructures.reduce((sum, s) => sum + s.capacite_max, 0) + 600; // +600 for existing structures
            document.getElementById('totalCapacity').textContent = totalCapacity;
        }

        // Edit structure (placeholder)
        function editStructure(id) {
            showCustomAlert('Modification', 'Fonctionnalité de modification à implémenter', 'info');
        }

        // Custom alert function
        function showCustomAlert(title, message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `fixed top-4 right-4 z-50 max-w-sm p-4 rounded-lg shadow-lg ${
                type === 'success' ? 'bg-green-100 border border-green-400 text-green-700 dark:bg-green-900 dark:text-green-200' :
                type === 'error' ? 'bg-red-100 border border-red-400 text-red-700 dark:bg-red-900 dark:text-red-200' :
                'bg-blue-100 border border-blue-400 text-blue-700 dark:bg-blue-900 dark:text-blue-200'
            }`;
            
            alertDiv.innerHTML = `
                <div class="flex justify-between items-start">
                    <div>
                        <h4 class="font-bold">${title}</h4>
                        <p class="text-sm mt-1">${message}</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-xl font-bold">×</button>
                </div>
            `;
            
            document.body.appendChild(alertDiv);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentElement) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // Initialize app when page loads
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>
</html>