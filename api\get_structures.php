<?php
require_once 'config.php';

try {
    $sql = "
        SELECT 
            s.*,
            ts.nom as type_nom,
            CASE 
                WHEN s.type_structure_id = 1 AND s.capacite_max - s.capacite_actuelle <= 5 THEN 1
                WHEN s.type_structure_id = 4 THEN 1  
                ELSE 0 
            END as urgence,
            CASE 
                WHEN s.type_structure_id = 1 THEN 'Accueil d\'urgence pour enfants en détresse'
                WHEN s.type_structure_id = 2 THEN 'Organisation non gouvernementale d\'aide aux enfants'
                WHEN s.type_structure_id = 4 THEN 'Centre spécialisé d\'encadrement'
                WHEN s.type_structure_id = 5 THEN 'Environnement familial d\'accueil'
                ELSE 'Centre d\'accueil pour enfants'
            END as description
        FROM structure s
        LEFT JOIN type_structure ts ON s.type_structure_id = ts.id
        WHERE s.active = 1
        ORDER BY s.nom
    ";
    
    $stmt = $pdo->query($sql);
    $structures = $stmt->fetchAll();
    
    // Formatage des données pour l'affichage
    foreach ($structures as &$structure) {
        $structure['latitude'] = (float) $structure['latitude'];
        $structure['longitude'] = (float) $structure['longitude'];
        $structure['capacite_max'] = (int) $structure['capacite_max'];
        $structure['capacite_actuelle'] = (int) $structure['capacite_actuelle'];
        $structure['urgence'] = (bool) $structure['urgence'];
    }
    
    echo json_encode([
        'success' => true,
        'structures' => $structures,
        'count' => count($structures)
    ]);
    
} catch(Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erreur lors de la récupération des structures: ' . $e->getMessage()
    ]);
}
?>