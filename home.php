<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>UmwanaVoice - Accueil</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">

  <style>
    :root {
      --bleu: #1e3d59;
      --bleu-clair: #2a4f70;
      --jaune: #f7b731;
      --jaune-foncé: #e0a800;
      --gris-fond: #f4f6fb;
      --carte-fond: #ffffff;
      --ombre: rgba(0, 0, 0, 0.08);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--gris-fond);
      color: #2c3e50;
    }

    a {
      text-decoration: none;
      color: inherit;
      transition: 0.3s ease;
    }

    a:hover {
      color: var(--jaune);
    }

    header {
      background: linear-gradient(to right, #1e3d59, #2a4f70);
      color: white;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 40px;
      box-shadow: 0 3px 10px var(--ombre);
    }

    .logo {
      display: flex;
      align-items: center;
    }

    .logo img {
      height: 45px;
      margin-right: 12px;
    }

    .logo h1 {
      font-size: 26px;
      font-weight: 700;
    }

    .btn-connexion {
      background-color: var(--jaune);
      border: none;
      padding: 10px 22px;
      border-radius: 8px;
      font-weight: 600;
      color: #fff;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .btn-connexion:hover {
      background-color: var(--jaune-foncé);
    }

    .hero {
      background: linear-gradient(to right, rgba(30, 61, 89, 0.85), rgba(30, 61, 89, 0.5)),
        url('https://images.unsplash.com/photo-1601758123927-196b3f29ba3a') center/cover no-repeat;
      color: white;
      padding: 100px 20px;
      text-align: center;
    }

    .hero h2 {
      font-size: 46px;
      margin-bottom: 20px;
    }

    .hero p {
      font-size: 18px;
      max-width: 700px;
      margin: 0 auto;
    }

    .titre-interface {
      text-align: center;
      margin: 60px 20px 20px;
      font-size: 30px;
      font-weight: 700;
      color: var(--bleu);
    }

    .interfaces {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 30px;
      padding: 30px 20px 60px;
    }

    .carte-interface {
      background-color: var(--carte-fond);
      width: 260px;
      padding: 30px 20px;
      border-radius: 16px;
      text-align: center;
      box-shadow: 0 10px 20px var(--ombre);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border-top: 4px solid var(--bleu);
    }

    .carte-interface:hover {
      transform: translateY(-6px);
      box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
    }

    .carte-interface h3 {
      margin-bottom: 15px;
      color: var(--bleu);
      font-size: 20px;
      font-weight: 600;
    }

    .carte-interface a {
      display: inline-block;
      margin-top: 15px;
      padding: 10px 18px;
      background-color: var(--bleu);
      color: white;
      border-radius: 6px;
      font-size: 14px;
      transition: background-color 0.3s ease;
    }

    .carte-interface a:hover {
      background-color: var(--bleu-clair);
    }

    .localisation {
      background-color: #fff;
      padding: 60px 20px;
      text-align: center;
    }

    .localisation h2 {
      color: var(--bleu);
      margin-bottom: 30px;
      font-size: 26px;
    }

    .map {
      width: 100%;
      max-width: 900px;
      height: 400px;
      margin: 0 auto;
      background-color: #eaeaea;
      border-radius: 12px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #555;
      font-weight: bold;
      font-size: 18px;
    }

    footer {
      background-color: var(--bleu);
      color: white;
      text-align: center;
      padding: 30px 20px;
      margin-top: 50px;
    }

    footer p {
      margin: 8px 0;
      font-size: 14px;
    }

    @media screen and (max-width: 768px) {
      .hero h2 {
        font-size: 32px;
      }

      .carte-interface {
        width: 90%;
      }

      .interfaces {
        padding: 20px 10px 40px;
      }
    }
  </style>
</head>

<body>

<header>
  <div class="logo">
    <img src="https://cdn-icons-png.flaticon.com/512/732/732212.png" alt="Logo">
    <h1>UmwanaVoice</h1>
  </div>
  <button class="btn-connexion" onclick="window.location='connexion.php'">Se Connecter</button>
</header>

<section class="hero">
  <h2>Bienvenue sur UmwanaVoice</h2>
  <p>Une plateforme nationale dédiée à la gestion, au suivi et à la protection des enfants vulnérables au Burundi.</p>
</section>

<section class="titre-interface">
  <h2>Choisissez votre interface de travail</h2>
</section>

<section class="interfaces">
  <div class="carte-interface">
    <h3>Admin</h3>
    <a href="login.php?role=admin">Accéder à l'interface</a>
  </div>
  <div class="carte-interface">
    <h3>Directeur des Centres</h3>
    <a href="login.php?role=directeur">Accéder à l'interface</a>
  </div>
  <div class="carte-interface">
    <h3>ONG</h3>
    <a href="login.php?role=ong">Accéder à l'interface</a>
  </div>
  <div class="carte-interface">
    <h3>Suivi des Enfants</h3>
    <a href="login.php?role=suivi">Accéder à l'interface</a>
  </div>
  <div class="carte-interface">
    <h3>Parrainage</h3>
    <a href="condition_parrainage.php">Accéder à l'interface</a>
  </div>
</section>

<section class="localisation">
  <h2>Traçabilité & Géolocalisation des Centres</h2>
  <div class="map">
    Carte interactive à intégrer ici (Google Maps ou Leaflet)
  </div>
</section>

<footer>
  <p><strong>UmwanaVoice</strong> - Plateforme du Ministère de la Solidarité Nationale</p>
  <p>Contact : <EMAIL> | +257 22 00 00 00</p>
  <p>&copy; 2025 Tous droits réservés</p>
</footer>

</body>
</html>
